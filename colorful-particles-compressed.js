/*彩色粒子特效-压缩版*/
!function(a,b){"object"==typeof exports&&"object"==typeof module?module.exports=b():"function"==typeof define&&define.amd?define([],b):"object"==typeof exports?exports.POWERMODE=b():a.POWERMODE=b()}(this,function(){return function(a){var b={};function c(e){if(b[e])return b[e].exports;var d=b[e]={exports:{},id:e,loaded:!1};return a[e].call(d.exports,d,d.exports,c),d.loaded=!0,d.exports}return c.m=a,c.c=b,c.p="",c(0)}([function(c,g,b){var d=document.createElement("canvas");d.width=window.innerWidth,d.height=window.innerHeight,d.style.cssText="position:fixed;top:0;left:0;pointer-events:none;z-index:999999",window.addEventListener("resize",function(){d.width=window.innerWidth,d.height=window.innerHeight}),document.body.appendChild(d);var a=d.getContext("2d"),n=[],j=0,k=120,f=k,p=!1;function l(r,q){return Math.random()*(q-r)+r}function m(r){return o.colorful?"hsla("+l((q=l(0,360))-10,q+10)+", 100%, "+l(50,80)+"%, 1)":window.getComputedStyle(r).color;var q}function e(){var t=document.activeElement,v;if("TEXTAREA"===t.tagName||"INPUT"===t.tagName&&"text"===t.getAttribute("type")){var u=b(1)(t,t.selectionStart);return v=t.getBoundingClientRect(),{x:u.left+v.left,y:u.top+v.top,color:m(t)}}var s=window.getSelection();if(s.rangeCount){var q=s.getRangeAt(0),r=q.startContainer;return"TEXT_NODE"===r.nodeType&&(r=r.parentNode),v=q.getBoundingClientRect(),{x:v.left,y:v.top,color:m(r)}}return{x:0,y:0,color:"transparent"}}function h(q,s,r){return{x:q,y:s,alpha:1,color:r,velocity:{x:-1+2*Math.random(),y:-3.5+2*Math.random()}}}function o(){var t=e(),s=5+Math.round(10*Math.random());for(;s--;)n[j]=h(t.x,t.y,t.color),j=(j+1)%500;if(f=k,!p&&requestAnimationFrame(i),o.shake){var r=1+2*Math.random(),q=r*(Math.random()>.5?-1:1),u=r*(Math.random()>.5?-1:1);document.body.style.marginLeft=q+"px",document.body.style.marginTop=u+"px",setTimeout(function(){document.body.style.marginLeft="",document.body.style.marginTop=""},75)}}function i(){f>0?(requestAnimationFrame(i),f--,p=!0):p=!1,a.clearRect(0,0,d.width,d.height);for(var q=0;q<n.length;++q){var r=n[q];r.alpha<=.1||(r.velocity.y+=.075,r.x+=r.velocity.x,r.y+=r.velocity.y,r.alpha*=.96,a.globalAlpha=r.alpha,a.fillStyle=r.color,a.fillRect(Math.round(r.x-1.5),Math.round(r.y-1.5),3,3))}}o.colorful=!1,o.shake=!0,requestAnimationFrame(i),c.exports=o},function(b,a){!function(){var d=["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderStyle","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing","tabSize","MozTabSize"],e=null!=window.mozInnerScreenX;function c(k,l,o){var h=o&&o.debug||!1;if(h){var i=document.querySelector("#input-textarea-caret-position-mirror-div");i&&i.parentNode.removeChild(i)}var f=document.createElement("div");f.id="input-textarea-caret-position-mirror-div",document.body.appendChild(f);var g=f.style,j=window.getComputedStyle?getComputedStyle(k):k.currentStyle;g.whiteSpace="pre-wrap","INPUT"!==k.nodeName&&(g.wordWrap="break-word"),g.position="absolute",h||(g.visibility="hidden"),d.forEach(function(p){g[p]=j[p]}),e?k.scrollHeight>parseInt(j.height)&&(g.overflowY="scroll"):g.overflow="hidden",f.textContent=k.value.substring(0,l),"INPUT"===k.nodeName&&(f.textContent=f.textContent.replace(/\s/g," "));var n=document.createElement("span");n.textContent=k.value.substring(l)||".",f.appendChild(n);var m={top:n.offsetTop+parseInt(j.borderTopWidth),left:n.offsetLeft+parseInt(j.borderLeftWidth)};return h?n.style.backgroundColor="#aaa":document.body.removeChild(f),m}"undefined"!=typeof b&&"undefined"!=typeof b.exports?b.exports=c:window.getCaretCoordinates=c}()}]);POWERMODE.colorful=!0,POWERMODE.shake=!1,document.body.addEventListener("input",POWERMODE);
